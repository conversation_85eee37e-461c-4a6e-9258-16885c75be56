"use client";

import React, { useState, useReducer, use<PERSON><PERSON>back, useMemo, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { 
  Search, 
  Utensils, 
  Plus, 
  Minus, 
  X, 
  ShoppingCart,
  Check,
  Pizza,
  StickyNote
} from "lucide-react";

// Hooks and utilities
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useStaffMenuV4 } from '@/lib/hooks/useStaffMenuV4';
import { useSupplements } from '@/lib/hooks/useSupplements';

// Types
import { MenuItem } from '@/lib/db/v4/schemas/menu-schema';
import { OrderType } from '@/lib/types/order-types';
import { NewOrder } from '@/lib/db/v4/schemas/order-schema';

// Order types and interfaces
interface OrderAddon {
  id: string;
  name: string;
  price: number;
}

interface PizzaQuarter {
  id: string;
  name: string;
  price: number;
}

interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  size: string;
  price: number;
  quantity: number;
  addons: OrderAddon[];
  notes: string;
  categoryId: string;
  compositeType?: 'pizza_quarters';
  quarters?: PizzaQuarter[];
}

interface OrderState {
  items: OrderItem[];
  total: number;
  orderType: OrderType;
  tableId: string;
  notes: string;
}

interface UiState {
  selectedCategory: string;
  searchQuery: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  lastAddedItem: string | null;
  showTableSelection: boolean;
}

// Initial states
const initialOrderState: OrderState = {
  items: [],
  total: 0,
  orderType: 'dine-in', // Fixed to dine-in only
  tableId: '',
  notes: ''
};

const initialUiState: UiState = {
  selectedCategory: '',
  searchQuery: '',
  selectedItemForAddons: null,
  selectedItemSizes: {},
  selectedAddons: {},
  itemNotes: {},
  lastAddedItem: null,
  showTableSelection: false
};

// Utility functions
const getItemColor = (categoryId: string, itemId: string, categories: any[]): string => {
  const category = categories?.find(cat => cat.id === categoryId);
  return category?.color || '#f3f4f6';
};

const getLightColor = (color: string): string => {
  // Simple function to lighten a color
  return color + '20'; // Add transparency
};

// Order reducer actions
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, updates: Partial<OrderItem> } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons.reduce((sum, addon) => sum + addon.price, 0) * item.quantity;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return initialOrderState;
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'UPDATE_ITEM': {
      const { itemId, updates } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, ...updates } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const targetItem = state.items.find(item => item.id === itemId);
      
      if (!targetItem || targetItem.quantity <= 1) {
        const updatedItems = state.items.filter(item => item.id !== itemId);
        return {
          ...state,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        };
      }
      
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity - 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const { itemId } = action.payload;
      const updatedItems = state.items.filter(item => item.id !== itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_TABLE': {
      return {
        ...state,
        tableId: action.payload.tableId
      };
    }
      
    case 'SET_NOTES': {
      return {
        ...state,
        notes: action.payload.notes
      };
    }
      
    default:
      return state;
  }
};

// Main component
const MobileOrderingInterface: React.FC = () => {
  // Hooks
  const { isAuthenticated, user } = useAuth();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { createOrder } = useOrderV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  // Show table selection if no table is selected
  useEffect(() => {
    if (!orderState.tableId && tablesReady) {
      setUiState(prev => ({ ...prev, showTableSelection: true }));
    }
  }, [orderState.tableId, tablesReady]);

  // Set initial category when menu loads
  useEffect(() => {
    if (menuReady && categories && categories.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [menuReady, categories, uiState.selectedCategory]);

  // Helper functions
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  const getItemNoteKey = useCallback((itemId: string, size: string) => `${itemId}-${size}-note`, []);

  // Handler functions
  const handleIncrement = useCallback((itemId: string) => {
    dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleDecrement = useCallback((itemId: string) => {
    dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleRemoveItem = useCallback((itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });
  }, []);

  const handleItemEdit = useCallback((item: OrderItem) => {
    // Set up customization panel for editing
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${item.menuItemId}-${item.size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.menuItemId]: item.size },
      selectedAddons: {
        ...prev.selectedAddons,
        [getAddonKey(item.menuItemId, item.size)]: new Set(item.addons.map(a => a.id))
      },
      itemNotes: {
        ...prev.itemNotes,
        [getItemNoteKey(item.menuItemId, item.size)]: item.notes
      }
    }));
  }, [getAddonKey, getItemNoteKey]);

  const handleItemSelect = useCallback((itemId: string, size: string) => {
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${itemId}-${size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [itemId]: size }
    }));
  }, []);

  const handleAddItem = useCallback(async (item: MenuItem, size: string) => {
    // Find category for this item
    let categoryId = uiState.selectedCategory;
    for (const category of categories || []) {
      const foundItem = category.items?.find((menuItem: MenuItem) => menuItem.id === item.id);
      if (foundItem) {
        categoryId = category.id;
        break;
      }
    }

    const selectedAddons = uiState.selectedAddons[getAddonKey(item.id, size)] || new Set();
    const itemNotes = uiState.itemNotes[getItemNoteKey(item.id, size)] || '';

    // For now, create empty addons array - will be populated by customization panel
    const validAddonObjects: OrderAddon[] = [];

    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: validAddonObjects,
        notes: itemNotes,
        categoryId
      }
    });

    // Set visual feedback
    const signature = `${item.id}-${size}`;
    setUiState(prev => ({ ...prev, lastAddedItem: signature }));

    // Open customization panel
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${item.id}-${size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size }
    }));

    toast({
      title: "Article ajouté",
      description: `${item.name} (${size}) ajouté à la commande`,
    });
  }, [uiState.selectedCategory, uiState.selectedAddons, uiState.itemNotes, categories, getAddonKey, getItemNoteKey, dispatch, toast]);

  const handlePlaceOrder = useCallback(async () => {
    if (orderState.items.length === 0) {
      toast({
        title: "Commande vide",
        description: "Ajoutez des articles avant de passer la commande",
        variant: "destructive"
      });
      return;
    }

    if (!orderState.tableId) {
      toast({
        title: "Table non sélectionnée",
        description: "Sélectionnez une table pour continuer",
        variant: "destructive"
      });
      return;
    }

    setIsPlacingOrder(true);

    try {
      const newOrder: NewOrder = {
        tableId: orderState.tableId,
        orderType: 'dine-in',
        status: 'pending',
        items: orderState.items.map(item => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "✅ Commande créée",
        description: `Commande pour la table ${tables?.find(t => t.id === orderState.tableId)?.name} créée avec succès`,
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, tables, toast]);

  const handleCustomizationConfirm = useCallback(async () => {
    if (!uiState.selectedItemForAddons) return;

    const [itemId, sizeFromKey] = uiState.selectedItemForAddons.split('-');
    const selectedSize = uiState.selectedItemSizes[itemId] || sizeFromKey || 'default';

    // Find the most recent item in the order that matches this item and size
    const targetItem = [...orderState.items].reverse().find(item =>
      item.menuItemId === itemId && item.size === selectedSize
    );

    if (targetItem) {
      // Find category for supplements
      let categoryId = uiState.selectedCategory;
      for (const category of categories || []) {
        const foundItem = category.items?.find((menuItem: MenuItem) => menuItem.id === itemId);
        if (foundItem) {
          categoryId = category.id;
          break;
        }
      }

      const selectedAddons = uiState.selectedAddons[getAddonKey(itemId, selectedSize)] || new Set();
      const itemNotes = uiState.itemNotes[getItemNoteKey(itemId, selectedSize)] || '';

      // Get supplements for the category (simplified for now)
      const validAddonObjects: OrderAddon[] = Array.from(selectedAddons)
        .map(addonId => ({
          id: addonId,
          name: `Supplement ${addonId}`,
          price: 0 // Will be populated by the supplements hook in the customization panel
        }));

      // Update the item
      dispatch({
        type: 'UPDATE_ITEM',
        payload: {
          itemId: targetItem.id,
          updates: {
            addons: validAddonObjects,
            notes: itemNotes
          }
        }
      });

      toast({
        title: "Article mis à jour",
        description: "Les personnalisations ont été appliquées",
      });
    }

    // Close the customization panel
    setUiState(prev => ({ ...prev, selectedItemForAddons: null }));
  }, [uiState.selectedItemForAddons, uiState.selectedItemSizes, uiState.selectedAddons, uiState.itemNotes, orderState.items, categories, getAddonKey, getItemNoteKey, dispatch, toast]);

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Mobile Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 shadow-sm">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                <Utensils className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Commande</h1>
                {orderState.tableId && (
                  <p className="text-sm text-gray-500">
                    Table {tables?.find(t => t.id === orderState.tableId)?.name}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {orderState.items.length > 0 && (
                <Badge variant="secondary" className="text-sm">
                  {orderState.items.reduce((sum, item) => sum + item.quantity, 0)} articles
                </Badge>
              )}
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Rechercher un article..."
              value={uiState.searchQuery}
              onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
              className="pl-10 h-11 bg-gray-50 border-gray-200 focus:bg-white rounded-xl"
            />
          </div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="flex-shrink-0 bg-white border-b border-gray-100">
        <ScrollArea className="w-full">
          <div className="flex gap-2 px-4 py-3">
            {menuLoading ? (
              [...Array(4)].map((_, i) => (
                <div key={i} className="h-10 w-24 bg-gray-200 rounded-xl animate-pulse flex-shrink-0" />
              ))
            ) : (
              categories?.map((category) => {
                const isActive = uiState.selectedCategory === category.id;
                return (
                  <Button
                    key={category.id}
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                    className={cn(
                      "flex-shrink-0 h-10 px-4 rounded-xl font-medium transition-all",
                      isActive
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "bg-white text-gray-600 border-gray-200 hover:bg-gray-50"
                    )}
                  >
                    <span className="mr-2">{category.emoji || '🍽️'}</span>
                    <span className="text-sm">{category.name}</span>
                  </Button>
                );
              })
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden flex">
        {/* Menu Items - Left Side */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-4">
              {menuLoading ? (
                <div className="space-y-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-32 bg-gray-200 rounded-xl animate-pulse" />
                  ))}
                </div>
              ) : (
                <MenuItemsGrid
                  categories={categories}
                  selectedCategory={uiState.selectedCategory}
                  searchQuery={uiState.searchQuery}
                  selectedItemForAddons={uiState.selectedItemForAddons}
                  selectedItemSizes={uiState.selectedItemSizes}
                  lastAddedItem={uiState.lastAddedItem}
                  onItemSelect={handleItemSelect}
                  onAddItem={handleAddItem}
                />
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Order Summary - Right Side */}
        <div className="w-80 border-l border-gray-200 bg-white flex flex-col">
          <MobileOrderSummary
            items={orderState.items}
            total={orderState.total}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onRemove={handleRemoveItem}
            onItemEdit={handleItemEdit}
            onPlaceOrder={handlePlaceOrder}
            isPlacingOrder={isPlacingOrder}
          />
        </div>
      </div>

      {/* Customization Panel Overlay */}
      {uiState.selectedItemForAddons && (
        <MobileCustomizationPanel
          selectedItemForAddons={uiState.selectedItemForAddons}
          selectedItemSizes={uiState.selectedItemSizes}
          selectedAddons={uiState.selectedAddons}
          itemNotes={uiState.itemNotes}
          categories={categories}
          getAddonKey={getAddonKey}
          getItemNoteKey={getItemNoteKey}
          onClose={() => setUiState(prev => ({ ...prev, selectedItemForAddons: null }))}
          onToggleAddon={(itemId: string, size: string, addonId: string) => {
            const key = getAddonKey(itemId, size);
            setUiState(prev => {
              const currentAddons = prev.selectedAddons[key] || new Set();
              const newAddons = new Set(currentAddons);
              if (newAddons.has(addonId)) {
                newAddons.delete(addonId);
              } else {
                newAddons.add(addonId);
              }
              return {
                ...prev,
                selectedAddons: { ...prev.selectedAddons, [key]: newAddons }
              };
            });
          }}
          onUpdateNote={(itemId: string, size: string, note: string) => {
            const key = getItemNoteKey(itemId, size);
            setUiState(prev => ({
              ...prev,
              itemNotes: { ...prev.itemNotes, [key]: note }
            }));
          }}
          onConfirm={handleCustomizationConfirm}
        />
      )}

      {/* Table Selection Dialog */}
      <Dialog open={uiState.showTableSelection} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Sélectionner une table</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-3 p-4">
            {tables?.map((table) => (
              <Button
                key={table.id}
                variant="outline"
                onClick={() => {
                  dispatch({ type: 'SET_TABLE', payload: { tableId: table.id } });
                  setUiState(prev => ({ ...prev, showTableSelection: false }));
                }}
                className="h-16 flex flex-col items-center justify-center hover:bg-primary/5"
              >
                <div className="text-lg font-bold text-gray-900">{table.name}</div>
                <div className="text-xs text-gray-500">{table.seats} places</div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// MenuItemsGrid Component
interface MenuItemsGridProps {
  categories: any[];
  selectedCategory: string;
  searchQuery: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  lastAddedItem: string | null;
  onItemSelect: (itemId: string, size: string) => void;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = ({
  categories,
  selectedCategory,
  searchQuery,
  selectedItemForAddons,
  selectedItemSizes,
  lastAddedItem,
  onItemSelect,
  onAddItem
}) => {
  // Filter items based on category and search
  const filteredItems = useMemo(() => {
    const currentCategory = categories?.find(cat => cat.id === selectedCategory);
    if (!currentCategory?.items) return [];

    let items = currentCategory.items;

    if (searchQuery.trim()) {
      items = items.filter((item: MenuItem) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return items;
  }, [categories, selectedCategory, searchQuery]);

  if (filteredItems.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-2">
          <Search className="h-12 w-12 mx-auto" />
        </div>
        <p className="text-gray-500">
          {searchQuery ? 'Aucun article trouvé' : 'Aucun article dans cette catégorie'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {filteredItems.map((item: MenuItem) => {
        const selectedSize = selectedItemSizes[item.id];
        const isSelected = selectedSize && selectedItemForAddons === `${item.id}-${selectedSize}`;

        return (
          <MobileMenuItemCard
            key={item.id}
            item={item}
            isSelected={isSelected}
            selectedSize={selectedSize}
            lastAddedItem={lastAddedItem}
            onSelect={onItemSelect}
            onAddItem={onAddItem}
          />
        );
      })}
    </div>
  );
};

// MobileMenuItemCard Component
interface MobileMenuItemCardProps {
  item: MenuItem;
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onSelect: (itemId: string, size: string) => void;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MobileMenuItemCard: React.FC<MobileMenuItemCardProps> = ({
  item,
  isSelected,
  selectedSize,
  lastAddedItem,
  onSelect,
  onAddItem
}) => {
  const handleSizeClick = useCallback((size: string) => {
    onAddItem(item, size);
  }, [item, onAddItem]);

  const hasMultipleSizes = Object.keys(item.prices).length > 1;

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      isSelected && "ring-2 ring-primary/40 shadow-md"
    )}>
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-lg text-gray-900 mb-1">{item.name}</h3>
            {item.description && (
              <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>
            )}
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {Object.entries(item.prices).map(([size, price]) => {
            const isJustAdded = lastAddedItem === `${item.id}-${size}`;
            const isThisSizeSelected = isSelected && selectedSize === size;

            return (
              <Button
                key={`${item.id}-${size}`}
                variant={isThisSizeSelected ? "default" : "outline"}
                size="sm"
                onClick={() => handleSizeClick(size)}
                className={cn(
                  "flex-1 min-w-[120px] h-12 transition-all duration-200",
                  isThisSizeSelected && "bg-primary text-primary-foreground",
                  isJustAdded && "ring-2 ring-green-400 bg-green-50 text-green-700 border-green-300",
                  !hasMultipleSizes && "w-full"
                )}
              >
                <div className="flex flex-col items-center">
                  <span className="font-medium">
                    {size === "default" ? "Classique" : size}
                  </span>
                  <span className={cn(
                    "text-xs font-bold",
                    isThisSizeSelected ? "text-primary-foreground" : "text-primary"
                  )}>
                    {price as number} DA
                  </span>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// MobileOrderSummary Component
interface MobileOrderSummaryProps {
  items: OrderItem[];
  total: number;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  onItemEdit: (item: OrderItem) => void;
  onPlaceOrder: () => void;
  isPlacingOrder: boolean;
}

const MobileOrderSummary: React.FC<MobileOrderSummaryProps> = ({
  items,
  total,
  onIncrement,
  onDecrement,
  onRemove,
  onItemEdit,
  onPlaceOrder,
  isPlacingOrder
}) => {
  const totalItemCount = useMemo(() => {
    return items.reduce((sum, item) => sum + item.quantity, 0);
  }, [items]);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Commande</h2>
          <Badge variant="secondary" className="text-sm">
            {totalItemCount} article{totalItemCount > 1 ? 's' : ''}
          </Badge>
        </div>
      </div>

      {/* Items List */}
      <div className="flex-1 overflow-hidden">
        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-6 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <ShoppingCart className="h-8 w-8 text-gray-400" />
            </div>
            <p className="text-gray-500 mb-2">Aucun article</p>
            <p className="text-sm text-gray-400">Sélectionnez des articles pour commencer</p>
          </div>
        ) : (
          <ScrollArea className="h-full">
            <div className="p-4 space-y-3">
              {items.map((item) => (
                <Card key={item.id} className="border border-gray-200">
                  <CardContent className="p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 text-sm">{item.name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {item.size === 'default' ? 'Classique' : item.size}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {item.price} DA
                          </span>
                        </div>
                        {item.addons.length > 0 && (
                          <div className="mt-1">
                            <p className="text-xs text-gray-600">
                              + {item.addons.map(addon => addon.name).join(', ')}
                            </p>
                          </div>
                        )}
                        {item.notes && (
                          <div className="mt-1">
                            <p className="text-xs text-gray-600 italic">
                              Note: {item.notes}
                            </p>
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onRemove(item.id)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-auto"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDecrement(item.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="font-medium text-sm min-w-[2rem] text-center">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onIncrement(item.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-sm">
                          {((item.price + item.addons.reduce((sum, addon) => sum + addon.price, 0)) * item.quantity).toFixed(0)} DA
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onItemEdit(item)}
                          className="text-xs text-primary hover:text-primary/80 p-0 h-auto"
                        >
                          Modifier
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Footer with Total and Place Order */}
      {items.length > 0 && (
        <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
          <div className="flex items-center justify-between mb-3">
            <span className="text-lg font-semibold text-gray-900">Total</span>
            <span className="text-xl font-bold text-primary">{total.toFixed(0)} DA</span>
          </div>
          <Button
            onClick={onPlaceOrder}
            disabled={isPlacingOrder}
            className="w-full h-12 text-base font-semibold"
          >
            {isPlacingOrder ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Création...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Passer la commande
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

// MobileCustomizationPanel Component
interface MobileCustomizationPanelProps {
  selectedItemForAddons: string;
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  categories: any[];
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  onClose: () => void;
  onToggleAddon: (itemId: string, size: string, addonId: string) => void;
  onUpdateNote: (itemId: string, size: string, note: string) => void;
  onConfirm: () => void;
}

const MobileCustomizationPanel: React.FC<MobileCustomizationPanelProps> = ({
  selectedItemForAddons,
  selectedItemSizes,
  selectedAddons,
  itemNotes,
  categories,
  getAddonKey,
  getItemNoteKey,
  onClose,
  onToggleAddon,
  onUpdateNote,
  onConfirm
}) => {
  // Parse the selected item
  const [itemId, sizeFromKey] = selectedItemForAddons.split('-');
  const selectedSize = selectedItemSizes[itemId] || sizeFromKey || 'default';

  // Find the item and category
  let item: MenuItem | null = null;
  let category: any = null;

  for (const cat of categories || []) {
    const foundItem = cat.items?.find((menuItem: MenuItem) => menuItem.id === itemId);
    if (foundItem) {
      item = foundItem;
      category = cat;
      break;
    }
  }

  if (!item || !category) {
    return null;
  }

  const addonKey = getAddonKey(itemId, selectedSize);
  const noteKey = getItemNoteKey(itemId, selectedSize);
  const currentAddons = selectedAddons[addonKey] || new Set();
  const currentNote = itemNotes[noteKey] || '';

  return (
    <div className="fixed inset-0 bg-black/50 flex items-end z-50">
      <div className="w-full bg-white rounded-t-2xl max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
              <p className="text-sm text-gray-500">
                {selectedSize === 'default' ? 'Classique' : selectedSize} - {item.prices[selectedSize]} DA
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-4 space-y-6">
              {/* Supplements Section */}
              <SupplementsSection
                categoryId={category.id}
                selectedSize={selectedSize}
                selectedAddons={currentAddons}
                onToggleAddon={(addonId: string) => onToggleAddon(itemId, selectedSize, addonId)}
              />

              {/* Pizza Builder Section (if applicable) */}
              {category.name.toLowerCase().includes('pizza') && (
                <PizzaBuilderSection
                  categoryId={category.id}
                  selectedSize={selectedSize}
                  categories={categories}
                />
              )}

              {/* Notes Section */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <StickyNote className="h-4 w-4 text-gray-500" />
                  <h4 className="font-medium text-gray-900">Notes spéciales</h4>
                </div>
                <Input
                  placeholder="Ajouter une note (ex: sans oignons, bien cuit...)"
                  value={currentNote}
                  onChange={(e) => onUpdateNote(itemId, selectedSize, e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Annuler
            </Button>
            <Button
              onClick={onConfirm}
              className="flex-1"
            >
              <Check className="h-4 w-4 mr-2" />
              Confirmer
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// SupplementsSection Component
interface SupplementsSectionProps {
  categoryId: string;
  selectedSize: string;
  selectedAddons: Set<string>;
  onToggleAddon: (addonId: string) => void;
}

const SupplementsSection: React.FC<SupplementsSectionProps> = ({
  categoryId,
  selectedSize,
  selectedAddons,
  onToggleAddon
}) => {
  const { supplements, isLoading } = useSupplements(categoryId);

  const activeSupplements = supplements.filter(supplement => supplement.isActive !== false);

  if (isLoading) {
    return (
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Suppléments</h4>
        <div className="space-y-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  if (activeSupplements.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900">Suppléments</h4>
      <div className="space-y-2">
        {activeSupplements.map((supplement) => {
          const price = supplement.prices[selectedSize] || Object.values(supplement.prices)[0] || 0;
          const isSelected = selectedAddons.has(supplement.id);

          return (
            <div
              key={supplement.id}
              onClick={() => onToggleAddon(supplement.id)}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all",
                isSelected
                  ? "border-primary bg-primary/5 text-primary"
                  : "border-gray-200 hover:border-gray-300"
              )}
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-5 h-5 rounded border-2 flex items-center justify-center",
                  isSelected ? "border-primary bg-primary" : "border-gray-300"
                )}>
                  {isSelected && <Check className="h-3 w-3 text-white" />}
                </div>
                <span className="font-medium">{supplement.name}</span>
              </div>
              <span className="font-semibold">+{price} DA</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// PizzaBuilderSection Component
interface PizzaBuilderSectionProps {
  categoryId: string;
  selectedSize: string;
  categories: any[];
}

const PizzaBuilderSection: React.FC<PizzaBuilderSectionProps> = ({
  categoryId,
  selectedSize,
  categories
}) => {
  const [quarters, setQuarters] = useState<(PizzaQuarter | null)[]>([null, null, null, null]);

  const currentCategory = categories?.find(cat => cat.id === categoryId);
  const pizzaItems = currentCategory?.items || [];

  const handleQuarterSelect = (quarterIndex: number, pizza: MenuItem) => {
    const price = pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0;
    const newQuarters = [...quarters];
    newQuarters[quarterIndex] = {
      id: pizza.id,
      name: pizza.name,
      price: price
    };
    setQuarters(newQuarters);
  };

  const filledQuarters = quarters.filter(q => q !== null).length;

  if (pizzaItems.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Pizza className="h-4 w-4 text-orange-500" />
        <h4 className="font-medium text-gray-900">Pizza Personnalisée</h4>
        <Badge variant="outline" className="text-xs">
          {filledQuarters}/4 quarts
        </Badge>
      </div>

      {/* Pizza Visual */}
      <div className="flex justify-center">
        <div className="relative w-32 h-32">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            {/* Pizza base */}
            <circle cx="50" cy="50" r="48" fill="#f3f4f6" stroke="#d1d5db" strokeWidth="2"/>

            {/* Quarter lines */}
            <line x1="50" y1="2" x2="50" y2="98" stroke="#d1d5db" strokeWidth="1"/>
            <line x1="2" y1="50" x2="98" y2="50" stroke="#d1d5db" strokeWidth="1"/>

            {/* Quarter labels */}
            {quarters.map((quarter, index) => {
              const positions = [
                { x: 75, y: 25 }, // Top Right
                { x: 75, y: 75 }, // Bottom Right
                { x: 25, y: 75 }, // Bottom Left
                { x: 25, y: 25 }  // Top Left
              ];

              return (
                <g key={index}>
                  <circle
                    cx={positions[index].x}
                    cy={positions[index].y}
                    r="8"
                    fill={quarter ? "#10b981" : "#e5e7eb"}
                    className="cursor-pointer"
                  />
                  <text
                    x={positions[index].x}
                    y={positions[index].y + 1}
                    textAnchor="middle"
                    fontSize="6"
                    fill="white"
                    className="pointer-events-none font-bold"
                  >
                    {index + 1}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>
      </div>

      {/* Quarter Selection */}
      <div className="grid grid-cols-2 gap-2">
        {quarters.map((quarter, index) => (
          <div key={index} className="space-y-2">
            <p className="text-sm font-medium text-gray-700">
              Quart {index + 1}
            </p>
            <select
              value={quarter?.id || ''}
              onChange={(e) => {
                const selectedPizza = pizzaItems.find((p: MenuItem) => p.id === e.target.value);
                if (selectedPizza) {
                  handleQuarterSelect(index, selectedPizza);
                }
              }}
              className="w-full p-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="">Choisir...</option>
              {pizzaItems.map((pizza: MenuItem) => (
                <option key={pizza.id} value={pizza.id}>
                  {pizza.name}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MobileOrderingInterface;
